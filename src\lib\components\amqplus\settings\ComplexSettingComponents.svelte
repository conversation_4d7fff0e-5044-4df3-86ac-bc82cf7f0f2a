<!-- ComplexSettingComponents.svelte -->
<script>
	import { Input } from '$lib/components/ui/input';
	import * as Select from '$lib/components/ui/select/index.js';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { Button } from '$lib/components/ui/button';
	import { Label } from '$lib/components/ui/label';
	import RangeSlider from 'svelte-range-slider-pips';
	import SongTypesSelection from './SongTypesSelection.svelte';

	let {
		config,
		editedValue = $bindable(),
		getNodeColor = () => '#6366f1',
		getTotalSongs = () => 20
	} = $props();
</script>

<!-- Song Types Selection -->
{#if config.type === 'complex-song-types-selection'}
	<SongTypesSelection {config} bind:editedValue {getNodeColor} {getTotalSongs} />

<!-- Song Categories -->
{:else if config.type === 'complex-song-categories'}
	<div class="space-y-8">
		<div class="text-center">
			<Label class="text-2xl font-bold text-gray-800">{config.label}</Label>
			<p class="mt-2 text-gray-600">Configure which song categories to include</p>
		</div>
		<div class="p-6 bg-white border border-gray-200 rounded-lg shadow-lg">
			<div class="flex items-center justify-center mb-6 space-x-4">
				<Label class="text-lg font-medium">Mode:</Label>
				<Select.Root bind:value={editedValue.mode}>
					<Select.Trigger class="w-48 text-base">
						{editedValue.mode === 'all' ? 'All Enabled' : editedValue.mode === 'counts' ? 'Specific Counts' : 'Percentages'}
					</Select.Trigger>
					<Select.Content>
						<Select.Item value="all">All Enabled</Select.Item>
						<Select.Item value="counts">Specific Counts</Select.Item>
						<Select.Item value="percentages">Percentages</Select.Item>
					</Select.Content>
				</Select.Root>
			</div>
			<div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
				<div class="p-4 space-y-4 bg-white border rounded-lg">
					<Label class="block text-lg font-medium text-center" style="color: {getNodeColor()}">Openings</Label>
					<div class="space-y-2">
						<div class="flex items-center space-x-2"><Checkbox bind:checked={editedValue.openings.standard} id="op-standard" /><Label for="op-standard" class="text-xs">Standard</Label></div>
						<div class="flex items-center space-x-2"><Checkbox bind:checked={editedValue.openings.instrumental} id="op-instrumental" /><Label for="op-instrumental" class="text-xs">Instrumental</Label></div>
						<div class="flex items-center space-x-2"><Checkbox bind:checked={editedValue.openings.chanting} id="op-chanting" /><Label for="op-chanting" class="text-xs">Chanting</Label></div>
						<div class="flex items-center space-x-2"><Checkbox bind:checked={editedValue.openings.character} id="op-character" /><Label for="op-character" class="text-xs">Character</Label></div>
					</div>
					{#if editedValue.mode === 'counts'}
						<Input type="number" bind:value={editedValue.counts.openings} min="0" placeholder="Count" class="text-xs" />
					{:else if editedValue.mode === 'percentages'}
						<div class="px-2">
							<RangeSlider values={[editedValue.percentages.openings]} min={0} max={100} step={1} pips pipstep={25} all="label" on:change={(e) => editedValue.percentages.openings = e.detail.value} --slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()} />
							<div class="mt-1 text-xs text-center text-gray-600">{editedValue.percentages.openings}%</div>
						</div>
					{/if}
				</div>
				<div class="p-4 space-y-4 bg-white border rounded-lg">
					<Label class="block text-lg font-medium text-center" style="color: {getNodeColor()}">Endings</Label>
					<div class="space-y-2">
						<div class="flex items-center space-x-2"><Checkbox bind:checked={editedValue.endings.standard} id="ed-standard" /><Label for="ed-standard" class="text-xs">Standard</Label></div>
						<div class="flex items-center space-x-2"><Checkbox bind:checked={editedValue.endings.instrumental} id="ed-instrumental" /><Label for="ed-instrumental" class="text-xs">Instrumental</Label></div>
						<div class="flex items-center space-x-2"><Checkbox bind:checked={editedValue.endings.chanting} id="ed-chanting" /><Label for="ed-chanting" class="text-xs">Chanting</Label></div>
						<div class="flex items-center space-x-2"><Checkbox bind:checked={editedValue.endings.character} id="ed-character" /><Label for="ed-character" class="text-xs">Character</Label></div>
					</div>
					{#if editedValue.mode === 'counts'}
						<Input type="number" bind:value={editedValue.counts.endings} min="0" placeholder="Count" class="text-xs" />
					{:else if editedValue.mode === 'percentages'}
						<div class="px-2">
							<RangeSlider values={[editedValue.percentages.endings]} min={0} max={100} step={1} pips pipstep={25} all="label" on:change={(e) => editedValue.percentages.endings = e.detail.value} --slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()} />
							<div class="mt-1 text-xs text-center text-gray-600">{editedValue.percentages.endings}%</div>
						</div>
					{/if}
				</div>
				<div class="p-4 space-y-4 bg-white border rounded-lg">
					<Label class="block text-lg font-medium text-center" style="color: {getNodeColor()}">Insert Songs</Label>
					<div class="space-y-2">
						<div class="flex items-center space-x-2"><Checkbox bind:checked={editedValue.inserts.standard} id="ins-standard" /><Label for="ins-standard" class="text-xs">Standard</Label></div>
						<div class="flex items-center space-x-2"><Checkbox bind:checked={editedValue.inserts.instrumental} id="ins-instrumental" /><Label for="ins-instrumental" class="text-xs">Instrumental</Label></div>
						<div class="flex items-center space-x-2"><Checkbox bind:checked={editedValue.inserts.chanting} id="ins-chanting" /><Label for="ins-chanting" class="text-xs">Chanting</Label></div>
						<div class="flex items-center space-x-2"><Checkbox bind:checked={editedValue.inserts.character} id="ins-character" /><Label for="ins-character" class="text-xs">Character</Label></div>
					</div>
					{#if editedValue.mode === 'counts'}
						<Input type="number" bind:value={editedValue.counts.inserts} min="0" placeholder="Count" class="text-xs" />
					{:else if editedValue.mode === 'percentages'}
						<div class="px-2">
							<RangeSlider values={[editedValue.percentages.inserts]} min={0} max={100} step={1} pips pipstep={25} all="label" on:change={(e) => editedValue.percentages.inserts = e.detail.value} --slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()} />
							<div class="mt-1 text-xs text-center text-gray-600">{editedValue.percentages.inserts}%</div>
						</div>
					{/if}
				</div>
			</div>
		</div>
	</div>

<!-- Song Difficulty -->
{:else if config.type === 'complex-song-difficulty'}
	<div class="space-y-8">
		<div class="text-center">
			<Label class="text-2xl font-bold text-gray-800">{config.label}</Label>
			<p class="mt-2 text-gray-600">Configure difficulty distribution for songs</p>
		</div>
		<div class="p-6 bg-white border border-gray-200 rounded-lg shadow-lg">
			<div class="flex items-center justify-center mb-6 space-x-4">
				<Label class="text-lg font-medium">Mode:</Label>
				<Select.Root bind:value={editedValue.mode}>
					<Select.Trigger class="w-48 text-base">
						{editedValue.mode === 'all' ? 'All Enabled' : editedValue.mode === 'counts' ? 'Specific Counts' : 'Percentages'}
					</Select.Trigger>
					<Select.Content>
						<Select.Item value="all">All Enabled</Select.Item>
						<Select.Item value="counts">Specific Counts</Select.Item>
						<Select.Item value="percentages">Percentages</Select.Item>
					</Select.Content>
				</Select.Root>
			</div>
			<div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
				<div class="p-4 space-y-4 bg-white border rounded-lg">
					<div class="flex items-center justify-center space-x-2">
						<Checkbox bind:checked={editedValue.easy.enabled} id="easy" />
						<Label for="easy" class="text-lg font-medium" style="color: {getNodeColor()}">Easy</Label>
					</div>
					{#if editedValue.easy.enabled && editedValue.mode === 'percentages'}
						<div class="px-2">
							<RangeSlider values={[editedValue.easy.percentage]} min={0} max={100} step={1} pips pipstep={25} all="label" on:change={(e) => editedValue.easy.percentage = e.detail.value} --slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()} />
							<div class="mt-1 text-xs text-center text-gray-600">{editedValue.easy.percentage}%</div>
						</div>
					{:else if editedValue.easy.enabled && editedValue.mode === 'counts'}
						<Input type="number" bind:value={editedValue.easy.count} min="0" class="text-xs" />
					{/if}
				</div>
				<div class="p-4 space-y-4 bg-white border rounded-lg">
					<div class="flex items-center justify-center space-x-2">
						<Checkbox bind:checked={editedValue.medium.enabled} id="medium" />
						<Label for="medium" class="text-lg font-medium" style="color: {getNodeColor()}">Medium</Label>
					</div>
					{#if editedValue.medium.enabled && editedValue.mode === 'percentages'}
						<div class="px-2">
							<RangeSlider values={[editedValue.medium.percentage]} min={0} max={100} step={1} pips pipstep={25} all="label" on:change={(e) => editedValue.medium.percentage = e.detail.value} --slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()} />
							<div class="mt-1 text-xs text-center text-gray-600">{editedValue.medium.percentage}%</div>
						</div>
					{:else if editedValue.medium.enabled && editedValue.mode === 'counts'}
						<Input type="number" bind:value={editedValue.medium.count} min="0" class="text-xs" />
					{/if}
				</div>
				<div class="p-4 space-y-4 bg-white border rounded-lg">
					<div class="flex items-center justify-center space-x-2">
						<Checkbox bind:checked={editedValue.hard.enabled} id="hard" />
						<Label for="hard" class="text-lg font-medium" style="color: {getNodeColor()}">Hard</Label>
					</div>
					{#if editedValue.hard.enabled && editedValue.mode === 'percentages'}
						<div class="px-2">
							<RangeSlider values={[editedValue.hard.percentage]} min={0} max={100} step={1} pips pipstep={25} all="label" on:change={(e) => editedValue.hard.percentage = e.detail.value} --slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()} />
							<div class="mt-1 text-xs text-center text-gray-600">{editedValue.hard.percentage}%</div>
						</div>
					{:else if editedValue.hard.enabled && editedValue.mode === 'counts'}
						<Input type="number" bind:value={editedValue.hard.count} min="0" class="text-xs" />
					{/if}
				</div>
			</div>
		</div>
	</div>

<!-- Score Range -->
{:else if config.type === 'complex-score-range'}
	<div class="space-y-6">
		<Label class="text-lg font-semibold">{config.label}</Label>
		<div class="space-y-4">
			<div class="flex items-center space-x-4">
				<Label class="text-sm font-medium">Mode:</Label>
				<Select.Root bind:value={editedValue.mode}>
					<Select.Trigger class="w-32">{editedValue.mode === 'range' ? 'Range' : 'Percentages'}</Select.Trigger>
					<Select.Content>
						<Select.Item value="range">Range</Select.Item>
						<Select.Item value="percentages">Percentages</Select.Item>
					</Select.Content>
				</Select.Root>
			</div>
			{#if editedValue.mode === 'range'}
				<div class="flex items-center space-x-4">
					<div class="space-y-1">
						<Label for="min-score" class="text-xs">Min Score</Label>
						<Input id="min-score" type="number" bind:value={editedValue.min} min={config.min} max={config.max} class="w-20 text-xs" />
					</div>
					<div class="space-y-1">
						<Label for="max-score" class="text-xs">Max Score</Label>
						<Input id="max-score" type="number" bind:value={editedValue.max} min={config.min} max={config.max} class="w-20 text-xs" />
					</div>
				</div>
			{:else}
				<div class="grid grid-cols-2 gap-4 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8">
					{#each Array.from({length: config.max - config.min + 1}, (_, i) => config.min + i) as score}
						<div class="space-y-2">
							<Label for="score-{score}" class="text-xs">Score {score}</Label>
							<div class="px-2">
								<RangeSlider values={[editedValue.percentages[score] || 0]} min={0} max={100} step={1} pips pipstep={25} all="label" on:change={(e) => editedValue.percentages[score] = e.detail.value} --slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()} />
								<div class="mt-1 text-xs text-center text-gray-600">{editedValue.percentages[score] || 0}%</div>
							</div>
						</div>
					{/each}
				</div>
			{/if}
		</div>
	</div>

<!-- Vintage -->
{:else if config.type === 'complex-vintage'}
	<div class="space-y-6">
		<Label class="text-lg font-semibold">{config.label}</Label>
		<div class="space-y-4">
			<div class="space-y-3">
				{#each editedValue.ranges as range, index}
					<div class="p-4 space-y-3 border rounded-lg">
						<div class="flex items-center justify-between">
							<Label class="text-sm font-medium">Range {index + 1}</Label>
							{#if editedValue.ranges.length > 1}
								<Button variant="outline" size="sm" onclick={() => { editedValue.ranges = editedValue.ranges.filter((_, i) => i !== index); }}>Remove</Button>
							{/if}
						</div>
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<Label class="text-xs">From</Label>
								<div class="flex space-x-2">
									<Select.Root bind:value={range.from.season}>
										<Select.Trigger class="w-24">{range.from.season}</Select.Trigger>
										<Select.Content><Select.Item value="Winter">Winter</Select.Item><Select.Item value="Spring">Spring</Select.Item><Select.Item value="Summer">Summer</Select.Item><Select.Item value="Fall">Fall</Select.Item></Select.Content>
									</Select.Root>
									<Input type="number" bind:value={range.from.year} min="1944" max="2025" class="w-20 text-xs" />
								</div>
							</div>
							<div class="space-y-2">
								<Label class="text-xs">To</Label>
								<div class="flex space-x-2">
									<Select.Root bind:value={range.to.season}>
										<Select.Trigger class="w-24">{range.to.season}</Select.Trigger>
										<Select.Content><Select.Item value="Winter">Winter</Select.Item><Select.Item value="Spring">Spring</Select.Item><Select.Item value="Summer">Summer</Select.Item><Select.Item value="Fall">Fall</Select.Item></Select.Content>
									</Select.Root>
									<Input type="number" bind:value={range.to.year} min="1944" max="2025" class="w-20 text-xs" />
								</div>
							</div>
						</div>
					</div>
				{/each}
			</div>
			<Button variant="outline" onclick={() => { editedValue.ranges = [...editedValue.ranges, { from: { season: 'Winter', year: 2020 }, to: { season: 'Fall', year: 2025 } }]; }}>Add Range</Button>
		</div>
	</div>

<!-- Genres/Tags -->
{:else if config.type === 'complex-genres-tags'}
	<div class="space-y-6">
		<Label class="text-lg font-semibold">{config.label}</Label>
		<div class="space-y-4">
			<div class="flex items-center space-x-4">
				<Label class="text-sm font-medium">Mode:</Label>
				<Select.Root bind:value={editedValue.mode}>
					<Select.Trigger class="w-32">{editedValue.mode === 'basic' ? 'Basic' : 'Advanced'}</Select.Trigger>
					<Select.Content><Select.Item value="basic">Basic</Select.Item><Select.Item value="advanced">Advanced</Select.Item></Select.Content>
				</Select.Root>
			</div>
			{#if editedValue.mode === 'basic'}
				<div class="grid grid-cols-3 gap-4">
					<div class="space-y-2">
						<Label class="text-sm font-medium text-green-600">Included</Label>
						<div class="min-h-[100px] border rounded p-2 text-xs text-gray-500">{editedValue.included.length > 0 ? editedValue.included.join(', ') : 'None selected'}</div>
					</div>
					<div class="space-y-2">
						<Label class="text-sm font-medium text-red-600">Excluded</Label>
						<div class="min-h-[100px] border rounded p-2 text-xs text-gray-500">{editedValue.excluded.length > 0 ? editedValue.excluded.join(', ') : 'None selected'}</div>
					</div>
					<div class="space-y-2">
						<Label class="text-sm font-medium text-blue-600">Optional</Label>
						<div class="min-h-[100px] border rounded p-2 text-xs text-gray-500">{editedValue.optional.length > 0 ? editedValue.optional.join(', ') : 'None selected'}</div>
					</div>
				</div>
			{:else}
				<div class="space-y-3">
					<Label class="text-sm font-medium">Advanced Editor</Label>
					<div class="text-sm text-gray-600">Advanced editor with percentage and count controls would be implemented here.</div>
				</div>
			{/if}
		</div>
	</div>
{/if}
